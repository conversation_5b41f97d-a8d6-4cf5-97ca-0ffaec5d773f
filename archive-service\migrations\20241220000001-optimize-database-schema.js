'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 1. Add missing foreign key constraints
      await queryInterface.addConstraint(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['user_id'],
          type: 'foreign key',
          name: 'fk_analysis_jobs_user_id',
          references: {
            table: {
              tableName: 'users',
              schema: 'auth'
            },
            field: 'id'
          },
          onDelete: 'CASCADE',
          onUpdate: 'CASCADE',
          transaction
        }
      );

      await queryInterface.addConstraint(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['result_id'],
          type: 'foreign key',
          name: 'fk_analysis_jobs_result_id',
          references: {
            table: {
              tableName: 'analysis_results',
              schema: 'archive'
            },
            field: 'id'
          },
          onDelete: 'SET NULL',
          onUpdate: 'CASCADE',
          transaction
        }
      );

      // 2. Add composite indexes for better query performance
      await queryInterface.addIndex(
        {
          tableName: 'analysis_results',
          schema: 'archive'
        },
        {
          fields: ['user_id', 'status', 'created_at'],
          name: 'idx_analysis_results_user_status_created',
          transaction
        }
      );

      await queryInterface.addIndex(
        {
          tableName: 'analysis_results',
          schema: 'archive'
        },
        {
          fields: ['status', 'created_at'],
          name: 'idx_analysis_results_status_created',
          transaction
        }
      );

      // 3. Add GIN index for JSONB persona_profile queries
      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_persona_profile_gin 
        ON archive.analysis_results USING GIN (persona_profile);
      `, { transaction });

      // 4. Add index for archetype queries
      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_archetype 
        ON archive.analysis_results ((persona_profile->>'archetype')) 
        WHERE status = 'completed' AND persona_profile IS NOT NULL;
      `, { transaction });

      // 5. Add composite index for demographic queries
      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_results_demographics 
        ON archive.analysis_results (user_id, status, created_at) 
        WHERE status = 'completed';
      `, { transaction });

      // 6. Optimize analysis_jobs indexes
      await queryInterface.addIndex(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['user_id', 'status', 'created_at'],
          name: 'idx_analysis_jobs_user_status_created',
          transaction
        }
      );

      await queryInterface.addIndex(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['status', 'created_at'],
          name: 'idx_analysis_jobs_status_created',
          transaction
        }
      );

      // 7. Add index for job cleanup queries
      await queryInterface.addIndex(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['created_at', 'status'],
          name: 'idx_analysis_jobs_created_status',
          transaction
        }
      );

      // 8. Add partial index for active jobs
      await queryInterface.sequelize.query(`
        CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_analysis_jobs_active 
        ON archive.analysis_jobs (user_id, created_at) 
        WHERE status IN ('queued', 'processing');
      `, { transaction });

      // 9. Add check constraints for data integrity
      await queryInterface.addConstraint(
        {
          tableName: 'analysis_results',
          schema: 'archive'
        },
        {
          type: 'check',
          name: 'chk_analysis_results_status',
          fields: ['status'],
          where: {
            status: ['completed', 'processing', 'failed']
          },
          transaction
        }
      );

      // 10. Add table comments for documentation
      await queryInterface.sequelize.query(`
        COMMENT ON TABLE archive.analysis_results IS 'Stores completed analysis results with persona profiles';
        COMMENT ON TABLE archive.analysis_jobs IS 'Tracks analysis job status and progress';
        COMMENT ON COLUMN archive.analysis_results.persona_profile IS 'JSONB field containing persona analysis results';
        COMMENT ON COLUMN archive.analysis_jobs.assessment_data IS 'JSONB field containing original assessment data';
      `, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Remove indexes
      await queryInterface.removeIndex(
        { tableName: 'analysis_results', schema: 'archive' },
        'idx_analysis_results_user_status_created',
        { transaction }
      );

      await queryInterface.removeIndex(
        { tableName: 'analysis_results', schema: 'archive' },
        'idx_analysis_results_status_created',
        { transaction }
      );

      await queryInterface.removeIndex(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'idx_analysis_jobs_user_status_created',
        { transaction }
      );

      await queryInterface.removeIndex(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'idx_analysis_jobs_status_created',
        { transaction }
      );

      await queryInterface.removeIndex(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'idx_analysis_jobs_created_status',
        { transaction }
      );

      // Remove GIN and partial indexes
      await queryInterface.sequelize.query(`
        DROP INDEX IF EXISTS archive.idx_analysis_results_persona_profile_gin;
        DROP INDEX IF EXISTS archive.idx_analysis_results_archetype;
        DROP INDEX IF EXISTS archive.idx_analysis_results_demographics;
        DROP INDEX IF EXISTS archive.idx_analysis_jobs_active;
      `, { transaction });

      // Remove constraints
      await queryInterface.removeConstraint(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'fk_analysis_jobs_user_id',
        { transaction }
      );

      await queryInterface.removeConstraint(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'fk_analysis_jobs_result_id',
        { transaction }
      );

      await queryInterface.removeConstraint(
        { tableName: 'analysis_results', schema: 'archive' },
        'chk_analysis_results_status',
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
