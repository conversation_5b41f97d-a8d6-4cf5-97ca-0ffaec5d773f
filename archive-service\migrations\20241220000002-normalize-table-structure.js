'use strict';

module.exports = {
  async up(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // 1. Remove redundant assessment_data from analysis_jobs
      // Since assessment_data is already stored in analysis_results, 
      // we don't need it duplicated in analysis_jobs
      
      // First, migrate any assessment_data from jobs to results if needed
      await queryInterface.sequelize.query(`
        UPDATE archive.analysis_results ar
        SET assessment_data = aj.assessment_data
        FROM archive.analysis_jobs aj
        WHERE ar.id = aj.result_id 
          AND ar.assessment_data IS NULL 
          AND aj.assessment_data IS NOT NULL;
      `, { transaction });

      // 2. Add a processing_started_at column to analysis_jobs for better tracking
      await queryInterface.addColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'processing_started_at',
        {
          type: Sequelize.DATE,
          allowNull: true,
          comment: 'Timestamp when job processing actually started'
        },
        { transaction }
      );

      // 3. Add priority field for job queue management
      await queryInterface.addColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'priority',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'Job priority (higher number = higher priority)'
        },
        { transaction }
      );

      // 4. Add retry_count for failed job handling
      await queryInterface.addColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'retry_count',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 0,
          comment: 'Number of retry attempts for failed jobs'
        },
        { transaction }
      );

      // 5. Add max_retries configuration
      await queryInterface.addColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'max_retries',
        {
          type: Sequelize.INTEGER,
          allowNull: false,
          defaultValue: 3,
          comment: 'Maximum number of retry attempts allowed'
        },
        { transaction }
      );

      // 6. Remove assessment_data from analysis_jobs after migration
      await queryInterface.removeColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'assessment_data',
        { transaction }
      );

      // 7. Add index for job queue processing
      await queryInterface.addIndex(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['status', 'priority', 'created_at'],
          name: 'idx_analysis_jobs_queue_processing',
          transaction
        }
      );

      // 8. Add index for retry logic
      await queryInterface.addIndex(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          fields: ['status', 'retry_count', 'max_retries'],
          name: 'idx_analysis_jobs_retry_logic',
          transaction
        }
      );

      // 9. Update existing jobs to set processing_started_at for completed jobs
      await queryInterface.sequelize.query(`
        UPDATE archive.analysis_jobs 
        SET processing_started_at = created_at 
        WHERE status IN ('completed', 'failed') 
          AND processing_started_at IS NULL;
      `, { transaction });

      // 10. Add check constraint for priority
      await queryInterface.addConstraint(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          type: 'check',
          name: 'chk_analysis_jobs_priority',
          fields: ['priority'],
          where: {
            priority: {
              [Sequelize.Op.gte]: 0
            }
          },
          transaction
        }
      );

      // 11. Add check constraint for retry_count
      await queryInterface.addConstraint(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        {
          type: 'check',
          name: 'chk_analysis_jobs_retry_count',
          fields: ['retry_count'],
          where: {
            retry_count: {
              [Sequelize.Op.gte]: 0
            }
          },
          transaction
        }
      );

      // 12. Create a view for job statistics
      await queryInterface.sequelize.query(`
        CREATE OR REPLACE VIEW archive.v_job_statistics AS
        SELECT 
          DATE_TRUNC('hour', created_at) as hour,
          status,
          COUNT(*) as job_count,
          AVG(EXTRACT(EPOCH FROM (COALESCE(completed_at, NOW()) - created_at))) as avg_duration_seconds,
          AVG(retry_count) as avg_retries
        FROM archive.analysis_jobs
        WHERE created_at >= NOW() - INTERVAL '7 days'
        GROUP BY DATE_TRUNC('hour', created_at), status
        ORDER BY hour DESC, status;
      `, { transaction });

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  },

  async down(queryInterface, Sequelize) {
    const transaction = await queryInterface.sequelize.transaction();
    
    try {
      // Drop view
      await queryInterface.sequelize.query(`
        DROP VIEW IF EXISTS archive.v_job_statistics;
      `, { transaction });

      // Remove constraints
      await queryInterface.removeConstraint(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'chk_analysis_jobs_priority',
        { transaction }
      );

      await queryInterface.removeConstraint(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'chk_analysis_jobs_retry_count',
        { transaction }
      );

      // Remove indexes
      await queryInterface.removeIndex(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'idx_analysis_jobs_queue_processing',
        { transaction }
      );

      await queryInterface.removeIndex(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'idx_analysis_jobs_retry_logic',
        { transaction }
      );

      // Add back assessment_data column
      await queryInterface.addColumn(
        {
          tableName: 'analysis_jobs',
          schema: 'archive'
        },
        'assessment_data',
        {
          type: Sequelize.JSONB,
          allowNull: true
        },
        { transaction }
      );

      // Remove new columns
      await queryInterface.removeColumn(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'processing_started_at',
        { transaction }
      );

      await queryInterface.removeColumn(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'priority',
        { transaction }
      );

      await queryInterface.removeColumn(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'retry_count',
        { transaction }
      );

      await queryInterface.removeColumn(
        { tableName: 'analysis_jobs', schema: 'archive' },
        'max_retries',
        { transaction }
      );

      await transaction.commit();
    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }
};
